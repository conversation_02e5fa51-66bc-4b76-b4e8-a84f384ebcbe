# 🚀 Lithuanian Lead Generation Workflow Setup Guide

## 📋 Prerequisites

1. **n8n Instance**: Running local n8n setup
2. **Google Sheets**: Access to Google Sheets with API credentials
3. **Apify Account**: API token for Google Maps scraper
4. **Twilio Account**: For SMS sending (will be added later)

## 🔧 Setup Steps

### Step 1: Import the Workflow

1. Open your n8n instance
2. Click "Import from File" or "Import from URL"
3. Upload the `lead_generation_workflow.json` file
4. The workflow will be imported with all nodes configured

### Step 2: Configure Google Sheets

1. **Create a new Google Sheet** with the following columns in row 1:
   ```
   A: business_name
   B: address  
   C: google_phone
   D: website
   E: owner_phone
   F: phone_source
   G: sms_sent
   H: sms_status
   I: reply_received
   J: notes
   K: google_maps_url
   L: category
   M: rating
   N: reviews_count
   ```

2. **Get the Sheet ID** from the URL:
   ```
   https://docs.google.com/spreadsheets/d/[SHEET_ID]/edit
   ```

3. **Update the workflow**:
   - Replace `YOUR_GOOGLE_SHEET_ID_HERE` in both Google Sheets nodes
   - Make sure the sheet name matches (default: "Leads")

4. **Configure Google Sheets credentials** in n8n:
   - Go to Credentials → Add Credential → Google Sheets API
   - Follow the OAuth setup process

### Step 3: Configure Apify Scraper

The Apify node is already configured with your API token. You can modify the search parameters:

**Current Configuration:**
- Location: Klaipeda, Lithuania
- Max results per search: 50
- Search terms: restaurant, cafe, shop, store, service, business

**To modify for other cities**, update the `locationQuery` in the Apify node:
- "Vilnius, Lithuania"
- "Kaunas, Lithuania" 
- "Siauliai, Lithuania"

### Step 4: Test the Workflow (Phase 1)

1. **Start with a small test**:
   - Reduce `maxCrawledPlacesPerSearch` to 10 in the Apify node
   - Click "Execute Workflow" on the Manual Trigger

2. **Expected Flow**:
   ```
   Manual Trigger → Apify Scraper → Filter (no website) → Save to Sheets → 
   Batch Processing → Rekvizitai Scraping → Update Sheets
   ```

3. **Check Results**:
   - Verify businesses appear in your Google Sheet
   - Check that only businesses without websites are included
   - Confirm phone numbers are populated (Google phone as fallback)

## 🎯 Current Workflow Features

### ✅ What's Working:
- ✅ Google Maps scraping via Apify
- ✅ Filtering businesses without websites  
- ✅ Google Sheets integration
- ✅ Batch processing (10 leads at a time)
- ✅ Rekvizitai URL generation
- ✅ Fallback to Google phone numbers

### 🚧 Next Phase (SMS Integration):
- 📱 Twilio SMS sending
- 📊 SMS delivery tracking
- 📈 Reply rate calculation
- 🔄 Reply webhook handling

## 🔍 Data Structure

Each lead will have:
- **business_name**: Company name from Google Maps
- **address**: Business address
- **google_phone**: Phone from Google Maps
- **website**: Empty (filtered out)
- **owner_phone**: From rekvizitai or fallback to google_phone
- **phone_source**: "owner", "google", or "missing"
- **sms_sent**: "pending", "success", "failed"
- **sms_status**: Delivery status details
- **reply_received**: "yes", "no"
- **notes**: Processing notes and URLs

## 🎨 Lithuanian SMS Template

```
Sveiki, čia Edgaras iš Upzera 👋 
Esame jauna komanda, padedanti verslams sukurti svetaines ir pritraukti daugiau klientų pagerinant matomumą internetinėje erdvėje. 
Norėčiau trumpai pasidalinti idėja kaip neprarasti potencialių klientų – ar domintų sužinoti daugiau?

upzera.com/lt
```

## 🚨 Important Notes

1. **Rate Limits**: Apify and rekvizitai scraping should respect rate limits
2. **Data Privacy**: Ensure compliance with Lithuanian data protection laws
3. **SMS Regulations**: Follow Lithuanian SMS marketing regulations
4. **Testing**: Always test with small batches first

## 🔧 Troubleshooting

### Common Issues:
1. **Google Sheets not updating**: Check credentials and sheet permissions
2. **Apify timeout**: Reduce batch size or add delays
3. **No businesses found**: Verify location query and search terms
4. **Rekvizitai scraping fails**: Check business name formatting

### Debug Steps:
1. Check n8n execution logs
2. Verify API responses in HTTP Request nodes
3. Test Code nodes with console.log statements
4. Validate Google Sheets permissions

## 📞 Next Steps

After verifying the Google Sheets integration works correctly:
1. Test with different Lithuanian cities
2. Refine business filtering logic
3. Add SMS integration with Twilio
4. Implement reply tracking system
5. Add success rate calculations

Ready to test? Start with the Manual Trigger and check your Google Sheet! 🎉
