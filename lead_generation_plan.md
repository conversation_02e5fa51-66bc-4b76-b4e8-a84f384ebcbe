# 🧠 Lead Generation Workflow for Web Development Projects

This is a structured plan for generating leads by identifying businesses **without a website** on Google Maps and contacting potential owners using SMS automation.

---

## 🚀 Step-by-Step Workflow

### 🟢 Step 1: Scrape Business Info from Google Maps

1. Search Google Maps for local businesses ( via the scraper service, perhaps serp api scraper).
2. For each business result:
   - ✅ Extract: `name`, `address`, `phone`, `website`
   - ❌ If **no website is listed**, mark it as a **potential lead**
3. Save results in a structured format (e.g., CSV, JSON, or database).
---

### 🟡 Step 2: Scrape Rekvizitai for Owner’s Number (it is a website where bussinesses have to register to be legit)

1. For each potential lead:
   - Format the business name into a URL-friendly slug: `rekvizitai.vz.lt/imone/{company-name}`
2. Use an HTTP request + HTML parser (Cheerio/Playwright/JS in n8n or custom code) to scrape:
   - Owner phone number (if listed)
3. Save owner number back to your lead record.

---

### 🔵 Step 3: Send SMS to Owner

1. Use a provider that supports **Lithuanian phone numbers**:
   - ✅ Options: **MessageBird**, **Twilio**, **Vonage**
2. Write a **predefined message** (include your name, service offer, and website link)
3. Send SMS using provider’s API (integrated via n8n HTTP node or custom script)
4. Log: timestamp, message content, delivery status

---

### 🟣 Step 4: Track Replies and Measure Reply Rate

1. Use webhook or SMS callback URL to collect replies (depending on provider)
2. Store replies in your DB (e.g., Supabase, PostgreSQL)
3. For each contact:
   - Track if they replied, and store reply content
4. Calculate reply rate:
   - `(number of replies / messages sent) * 100%`

---

## 🔧 Optional Tools

- **Supabase**: backend DB for leads and logs
- **n8n**: visual automation for scraping, filtering, sending, and tracking
- **Claude / IDE**: manage and refine workflow via MCP integration

---

## ✅ Final Tip

Avoid using `docker run --rm` for n8n if you want to persist user sessions and workflows.

