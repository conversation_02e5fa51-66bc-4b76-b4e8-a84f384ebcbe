{"name": "Update Successful SMS Records", "nodes": [{"parameters": {"jsCode": "// List of leads that were successfully sent SMS but not marked in database\nconst successfulLeads = [\n  {\n    \"business_name\": \"<PERSON><PERSON><PERSON> g<PERSON> ir suvenyrai\",\n    \"address\": \"Vytauto g. 32, <PERSON><PERSON><PERSON>, 59428 <PERSON>rienų r. sav., Lietuva\",\n    \"google_phone\": \"370 648 97779\",\n    \"owner_phone\": \"\",\n    \"website\": \"\",\n    \"phone_source\": \"google\",\n    \"reply_received\": \"no\",\n    \"notes\": \"Lead from Google Maps - no website found\",\n    \"google_maps_url\": \"https://www.google.com/maps/search/?api=1&query=Igeda%20g%C4%97l%C4%97s%20ir%20suvenyrai&query_place_id=ChIJTwvL491J50YR7VKMLRvhCjY\",\n    \"category\": \"Gėlininkas\",\n    \"rating\": \"5\",\n    \"reviews_count\": \"2\"\n  },\n  {\n    \"business_name\": \"Gėlė<PERSON>\",\n    \"address\": \"<PERSON><PERSON>žion<PERSON>, 59433 <PERSON>rienų r. sav., Lietuva\",\n    \"google_phone\": \"370 610 44687\",\n    \"owner_phone\": \"\",\n    \"website\": \"\",\n    \"phone_source\": \"google\",\n    \"reply_received\": \"no\",\n    \"notes\": \"Lead from Google Maps - no website found\",\n    \"google_maps_url\": \"https://www.google.com/maps/search/?api=1&query=G%C4%97l%C4%97s&query_place_id=ChIJofwVWgVL50YRDocX0yvhsT4\",\n    \"category\": \"Gėlių turgus\",\n    \"rating\": \"0\",\n    \"reviews_count\": \"0\"\n  },\n  {\n    \"business_name\": \"Pas Neringą\",\n    \"address\": \"Vilniaus Mažoji g. 2B, Trakai, 21118 Trakų r. sav., Lietuva\",\n    \"google_phone\": \"370 618 61159\",\n    \"owner_phone\": \"\",\n    \"website\": \"\",\n    \"phone_source\": \"google\",\n    \"reply_received\": \"no\",\n    \"notes\": \"Lead from Google Maps - no website found\",\n    \"google_maps_url\": \"https://www.google.com/maps/search/?api=1&query=Pas%20Nering%C4%85&query_place_id=ChIJBw4Fwqbz3UYRcXxEg2lGdUQ\",\n    \"category\": \"Gėlių turgus\",\n    \"rating\": \"5\",\n    \"reviews_count\": \"22\"\n  },\n  {\n    \"business_name\": \"GĖLĖS, salonas, D. Dzimidavičienės įmonė\",\n    \"address\": \"Draugystės g. 18, Elektrėnai, 26114 Elektrėnų sav., Lietuva\",\n    \"google_phone\": \"370 616 08802\",\n    \"owner_phone\": \"\",\n    \"website\": \"\",\n    \"phone_source\": \"google\",\n    \"reply_received\": \"no\",\n    \"notes\": \"Lead from Google Maps - no website found\",\n    \"google_maps_url\": \"https://www.google.com/maps/search/?api=1&query=G%C4%96L%C4%96S%2C%20salonas%2C%20D.%20Dzimidavi%C4%8Dien%C4%97s%20%C4%AFmon%C4%97&query_place_id=ChIJBe7GuB5n50YRHRUxvtN-88o\",\n    \"category\": \"Gėlininkas\",\n    \"rating\": \"4.4\",\n    \"reviews_count\": \"62\"\n  },\n  {\n    \"business_name\": \"Aldo gėlės\",\n    \"address\": \"Kamiliavos km. 2, 59450 Birštonas, Lietuva\",\n    \"google_phone\": \"370 682 13139\",\n    \"owner_phone\": \"\",\n    \"website\": \"\",\n    \"phone_source\": \"google\",\n    \"reply_received\": \"no\",\n    \"notes\": \"Lead from Google Maps - no website found\",\n    \"google_maps_url\": \"https://www.google.com/maps/search/?api=1&query=Aldo%20g%C4%97l%C4%97s&query_place_id=ChIJ1zs_g1xL50YRmHL13zK43tE\",\n    \"category\": \"Gėlininkas\",\n    \"rating\": \"5\",\n    \"reviews_count\": \"2\"\n  },\n  {\n    \"business_name\": \"Sirdgela\",\n    \"address\": \"Vilniaus g. 16, Vievis, 21372 Elektrėnų sav., Lietuva\",\n    \"google_phone\": \"370 684 84484\",\n    \"owner_phone\": \"\",\n    \"website\": \"\",\n    \"phone_source\": \"google\",\n    \"reply_received\": \"no\",\n    \"notes\": \"Lead from Google Maps - no website found\",\n    \"google_maps_url\": \"https://www.google.com/maps/search/?api=1&query=Sirdgela&query_place_id=ChIJ6U3LZyBh50YR0vV2P8TV_hc\",\n    \"category\": \"Gėlininkas\",\n    \"rating\": \"4.7\",\n    \"reviews_count\": \"14\"\n  }\n];\n\n// Add SMS success data to each lead\nconst updatedLeads = successfulLeads.map(lead => ({\n  ...lead,\n  sms_sent: \"success\",\n  sms_status: \"SMS sent successfully - retroactive update\",\n  sms_sent_date: new Date().toISOString().split('T')[0],\n  phone_used: lead.google_phone || lead.owner_phone || 'no_phone'\n}));\n\nconsole.log(`Preparing to update ${updatedLeads.length} successful SMS records`);\nreturn updatedLeads.map(data => ({ json: data }));"}, "id": "prepare-successful-leads-node-id", "name": "Prepare Successful Leads Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [100, 100]}], "connections": {}, "active": false, "settings": {"executionOrder": "v1"}}