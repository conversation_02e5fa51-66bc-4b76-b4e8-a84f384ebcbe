{"name": "Lithuanian Lead Generation Workflow", "nodes": [{"id": "manual-trigger", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [240, 300], "parameters": {"jsonOutput": "{\n  \"query\": \"restoran\",\n  \"location\": \"Klaipeda, Lithuania\"\n}"}}, {"id": "apify-scraper", "name": "Apify Google Maps Scraper", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [460, 300], "parameters": {"url": "https://api.apify.com/v2/acts/compass~crawler-google-places/run-sync-get-dataset-items?token=**********************************************", "method": "GET", "sendBody": true, "contentType": "json", "jsonBody": "{\n  \"includeWebResults\": false,\n  \"language\": \"en\",\n  \"locationQuery\": \"{{ $json.location }}\",\n  \"maxCrawledPlacesPerSearch\": 50,\n  \"maxImages\": 0,\n  \"maximumLeadsEnrichmentRecords\": 0,\n  \"scrapeContacts\": false,\n  \"scrapeDirectories\": false,\n  \"scrapeImageAuthors\": false,\n  \"scrapePlaceDetailPage\": false,\n  \"scrapeReviewsPersonalData\": true,\n  \"scrapeTableReservationProvider\": false,\n  \"searchStringsArray\": [\n    \"{{ $json.query }}\"\n  ],\n  \"skipClosedPlaces\": false\n}", "options": {"timeout": 300000}}}, {"id": "filter-no-website", "name": "Filter Businesses Without Website", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 300], "parameters": {"language": "javaScript", "mode": "runOnceForAllItems", "jsCode": "// Filter businesses without websites and format data\nconst businesses = $input.all();\nconst filteredLeads = [];\nconst seenBusinesses = new Set(); // Track duplicates within this batch\n\nfor (const item of businesses) {\n  const data = item.json;\n  \n  // Check if business has no website or website is empty/null\n  if (!data.website || data.website === '' || data.website === null) {\n    const businessName = data.title || data.name || 'Unknown';\n    const address = data.address || data.location || 'Unknown';\n    \n    // Create a unique key for deduplication (business name + address)\n    const uniqueKey = `${businessName.toLowerCase().trim()}_${address.toLowerCase().trim()}`;\n    \n    // Skip if we've already seen this business in this batch\n    if (seenBusinesses.has(uniqueKey)) {\n      console.log(`Skipping duplicate business: ${businessName}`);\n      continue;\n    }\n    \n    seenBusinesses.add(uniqueKey);\n    \n    const lead = {\n      business_name: businessName,\n      address: address,\n      google_phone: data.phone || data.phoneNumber || '',\n      website: '', // Empty since we're filtering for no website\n      owner_phone: '', // Will be filled later from rekvizitai\n      phone_source: 'pending', // pending/owner/google/missing\n      sms_sent: 'pending', // pending/success/failed\n      sms_status: '',\n      reply_received: 'no',\n      notes: 'Lead from Google Maps - no website found',\n      google_maps_url: data.url || '',\n      category: data.categoryName || data.category || '',\n      rating: data.totalScore || data.rating || '',\n      reviews_count: data.reviewsCount || '',\n      director_name: '', // Will be filled from rekvizitai\n      owner_email: '' // Will be filled from rekvizitai\n    };\n    \n    filteredLeads.push(lead);\n  }\n}\n\nconsole.log(`Found ${filteredLeads.length} unique businesses without websites (removed ${businesses.length - filteredLeads.length} duplicates)`);\n\nreturn filteredLeads.map(lead => ({ json: lead }));"}}, {"id": "save-initial-leads", "name": "Save Initial Leads to Google Sheets", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [900, 300], "parameters": {"operation": "append", "documentId": {"mode": "list", "value": "YOUR_GOOGLE_SHEET_ID_HERE"}, "sheetName": {"mode": "list", "value": "Leads"}, "range": "A:P", "options": {"locationDefine": "specifyRange"}, "columns": {"mappingMode": "defineBelow", "value": {"business_name": "={{ $json.business_name }}", "address": "={{ $json.address }}", "google_phone": "={{ $json.google_phone }}", "website": "={{ $json.website }}", "owner_phone": "={{ $json.owner_phone }}", "phone_source": "={{ $json.phone_source }}", "sms_sent": "={{ $json.sms_sent }}", "sms_status": "={{ $json.sms_status }}", "reply_received": "={{ $json.reply_received }}", "notes": "={{ $json.notes }}", "google_maps_url": "={{ $json.google_maps_url }}", "category": "={{ $json.category }}", "rating": "={{ $json.rating }}", "reviews_count": "={{ $json.reviews_count }}", "director_name": "={{ $json.director_name }}", "owner_email": "={{ $json.owner_email }}"}}}}, {"id": "batch-leads", "name": "Batch Leads for Processing", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 300], "parameters": {"language": "javaScript", "mode": "runOnceForAllItems", "jsCode": "// Process leads in batches of 10 for rekvizitai scraping\nconst leads = $input.all();\nconst batchSize = 10;\nconst batches = [];\n\nfor (let i = 0; i < leads.length; i += batchSize) {\n  const batch = leads.slice(i, i + batchSize);\n  batches.push({ json: { batch: batch.map(item => item.json), batchNumber: Math.floor(i / batchSize) + 1 } });\n}\n\nconsole.log(`Created ${batches.length} batches of leads`);\nreturn batches;"}}, {"id": "scrape-rekvizitai", "name": "Scrape Rekvizitai for Owner Info", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 300], "parameters": {"language": "javaScript", "mode": "runOnceForAllItems", "jsCode": "// Scrape rekvizitai.vz.lt for owner contact information\nconst inputItems = $input.all();\nconst enrichedLeads = [];\n\n// Process each batch\nfor (const batchItem of inputItems) {\n  const batch = batchItem.json.batch; // Extract the batch array\n  \n  // Process each lead in the batch\n  for (const lead of batch) {\n    try {\n      // Create URL-friendly slug from business name\n      const businessSlug = lead.business_name\n        .toLowerCase()\n        .replace(/[^a-z0-9\\s-]/g, '') // Remove special characters\n        .replace(/\\s+/g, '-') // Replace spaces with hyphens\n        .replace(/-+/g, '-') // Replace multiple hyphens with single\n        .trim();\n      \n      const rekvizitaiUrl = `https://rekvizitai.vz.lt/imone/${businessSlug}`;\n      \n      // Make HTTP request to scrape rekvizitai page\n      const response = await $http.get(rekvizitaiUrl, {\n        headers: {\n          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'\n        },\n        timeout: 10000\n      });\n      \n      if (response.status === 200) {\n        const html = response.data;\n        \n        // Extract phone numbers using regex\n        const phoneRegex = /\\+370\\s?\\d{2}\\s?\\d{6}|\\+370\\s?\\d{3}\\s?\\d{5}/g;\n        const phones = html.match(phoneRegex) || [];\n        \n        // Extract mobile phone (usually starts with +370 6)\n        const mobileRegex = /\\+370\\s?6\\d{2}\\s?\\d{5}/g;\n        const mobilePhones = html.match(mobileRegex) || [];\n        \n        // Extract director/owner name from the page\n        let directorName = '';\n        const directorMatch = html.match(/Vadovas[^<]*?([A-ZŠŽČĄĘĖĮŲŪ][a-zšžčąęėįų]+\\s+[A-ZŠŽČĄĘĖĮŲŪ][a-zšžčąęėįų]+)/i);\n        if (directorMatch) {\n          directorName = directorMatch[1].trim();\n        }\n        \n        // Extract email if available\n        const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}/g;\n        const emails = html.match(emailRegex) || [];\n        \n        // Determine best phone number to use\n        let ownerPhone = '';\n        let phoneSource = 'missing';\n        \n        if (mobilePhones.length > 0) {\n          ownerPhone = mobilePhones[0].replace(/\\s/g, '');\n          phoneSource = 'owner';\n        } else if (phones.length > 0) {\n          ownerPhone = phones[0].replace(/\\s/g, '');\n          phoneSource = 'owner';\n        } else if (lead.google_phone) {\n          ownerPhone = lead.google_phone;\n          phoneSource = 'google';\n        }\n        \n        const enrichedLead = {\n          ...lead,\n          rekvizitai_url: rekvizitaiUrl,\n          owner_phone: ownerPhone,\n          phone_source: phoneSource,\n          director_name: directorName,\n          owner_email: emails.length > 0 ? emails[0] : '',\n          notes: lead.notes + ` | Rekvizitai scraped successfully${directorName ? ` | Director: ${directorName}` : ''}`\n        };\n        \n        enrichedLeads.push(enrichedLead);\n        \n      } else {\n        // If rekvizitai page not found, fallback to Google phone\n        const enrichedLead = {\n          ...lead,\n          rekvizitai_url: rekvizitaiUrl,\n          owner_phone: lead.google_phone || '',\n          phone_source: lead.google_phone ? 'google' : 'missing',\n          notes: lead.notes + ' | Rekvizitai page not found (404)'\n        };\n        \n        enrichedLeads.push(enrichedLead);\n      }\n      \n      // Add delay between requests to be respectful\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n    } catch (error) {\n      console.error(`Error scraping rekvizitai for ${lead.business_name}:`, error.message);\n      \n      // Fallback to Google phone if rekvizitai fails\n      const enrichedLead = {\n        ...lead,\n        rekvizitai_url: `https://rekvizitai.vz.lt/imone/${lead.business_name.toLowerCase().replace(/[^a-z0-9\\s-]/g, '').replace(/\\s+/g, '-')}`,\n        owner_phone: lead.google_phone || '',\n        phone_source: lead.google_phone ? 'google' : 'missing',\n        notes: lead.notes + ` | Rekvizitai scraping failed: ${error.message}`\n      };\n      \n      enrichedLeads.push(enrichedLead);\n    }\n  }\n}\n\nconsole.log(`Processed ${enrichedLeads.length} leads with rekvizitai data`);\nreturn enrichedLeads.map(lead => ({ json: lead }));"}}, {"id": "remove-duplicates", "name": "Remove Duplicate Leads", "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 2, "position": [1380, 300], "parameters": {"operation": "removeDuplicateInputItems", "compare": "<PERSON><PERSON><PERSON>s", "fieldsToCompare": "business_name,address"}}, {"id": "update-leads-with-owner-info", "name": "Update Leads with Owner Info", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [1600, 300], "parameters": {"operation": "appendOrUpdate", "documentId": {"mode": "list", "value": "YOUR_GOOGLE_SHEET_ID_HERE"}, "sheetName": {"mode": "list", "value": "Leads"}, "range": "A:P", "options": {"locationDefine": "specifyRange"}, "columns": {"mappingMode": "defineBelow", "value": {"business_name": "={{ $json.business_name }}", "address": "={{ $json.address }}", "google_phone": "={{ $json.google_phone }}", "website": "={{ $json.website }}", "owner_phone": "={{ $json.owner_phone }}", "phone_source": "={{ $json.phone_source }}", "sms_sent": "={{ $json.sms_sent }}", "sms_status": "={{ $json.sms_status }}", "reply_received": "={{ $json.reply_received }}", "notes": "={{ $json.notes }}", "google_maps_url": "={{ $json.google_maps_url }}", "category": "={{ $json.category }}", "rating": "={{ $json.rating }}", "reviews_count": "={{ $json.reviews_count }}", "director_name": "={{ $json.director_name }}", "owner_email": "={{ $json.owner_email }}"}}}}], "connections": {"Manual Trigger": {"main": [[{"node": "Apify Google Maps Scraper", "type": "main", "index": 0}]]}, "Apify Google Maps Scraper": {"main": [[{"node": "Filter Businesses Without Website", "type": "main", "index": 0}]]}, "Filter Businesses Without Website": {"main": [[{"node": "Save Initial Leads to Google Sheets", "type": "main", "index": 0}]]}, "Save Initial Leads to Google Sheets": {"main": [[{"node": "Batch Leads for Processing", "type": "main", "index": 0}]]}, "Batch Leads for Processing": {"main": [[{"node": "Scrape Rekvizitai for Owner Info", "type": "main", "index": 0}]]}, "Scrape Rekvizitai for Owner Info": {"main": [[{"node": "Remove Duplicate Leads", "type": "main", "index": 0}]]}, "Remove Duplicate Leads": {"main": [[{"node": "Update Leads with Owner Info", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}}